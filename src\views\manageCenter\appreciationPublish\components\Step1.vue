<template>
  <div class="step1">
    <a-form :model="formData" :rules="rules" ref="formRef" :scroll-to-first-error="true">
      <!-- 服务类型板块 -->
      <div class="form-section">
        <h3 class="section-title">服务类型</h3>
        <div class="form-row">
          <a-form-item name="serviceType" class="service-type-item">
            <a-select
              v-model:value="formData.serviceType"
              @change="handleServiceTypeChange"
              placeholder="请选择服务类型"
              size="large"
              class="service-type-select"
              :disabled="props.isEditMode"
            >
              <a-select-option :value="2">发布资产处置</a-select-option>
              <a-select-option :value="3">发布采购信息</a-select-option>
            </a-select>
            <div v-if="props.isEditMode" class="edit-mode-tip">
              <span class="tip-text">编辑模式下不允许修改服务类型</span>
            </div>
          </a-form-item>
        </div>
      </div>

      <!-- 关联委托单号板块 -->
      <div class="form-section">
        <h3 class="section-title">关联委托单号</h3>
        <div class="form-row">
          <a-form-item name="entrustOrderId" class="entrust-order-item">
            <a-select
              v-model:value="formData.entrustOrderId"
              placeholder="请选择关联委托单号"
              size="large"
              class="entrust-order-select"
              :loading="entrustOrderLoading"
              show-search
              :filter-option="filterOption"
            >
              <a-select-option v-for="item in entrustOrderList" :key="item.id" :value="item.id" :label="item.entrustOrderNo">
                {{ item.entrustOrderNo }} - {{ item.entrustCompanyName }}
              </a-select-option>
            </a-select>
          </a-form-item>
        </div>
      </div>

      <!-- 委托信息板块 -->
      <div class="form-section">
        <div class="section-title">委托信息</div>
        <div class="section-content">
          <div class="form-row entrust-two-row">
            <a-form-item label="委托单位" :name="['entrustInfo', 'title']" required class="entrust-two-item">
              <a-input v-model:value="formData.entrustInfo.title" placeholder="委托单位" size="large" disabled readonly />
            </a-form-item>
            <a-form-item label="受委托单位" :name="['entrustInfo', 'type']" required class="entrust-two-item">
              <a-input v-model:value="formData.entrustInfo.type" placeholder="受委托单位" size="large" disabled readonly />
            </a-form-item>
          </div>

          <!-- 采购信息特有字段 -->
          <div v-if="formData.serviceType === 3" class="form-row">
            <a-form-item label="公告名称" :name="['entrustInfo', 'noticeName']" required class="full-width-item">
              <a-input v-model:value="formData.entrustInfo.noticeName" placeholder="请输入公告名称" size="large" />
            </a-form-item>
          </div>
        </div>
      </div>

      <!-- 基本信息板块（仅发布资产处置显示） -->
      <div v-if="formData.serviceType === 2" class="form-section">
        <div class="section-title">基本信息</div>
        <div class="section-content">
          <!-- 第一行：资产名称、资产编号、资产类型 -->
          <div class="form-row basic-three-row">
            <a-form-item label="资产名称" :name="['basicInfo', 'assetName']" required class="basic-three-item">
              <a-input v-model:value="formData.basicInfo.assetName" placeholder="请输入资产名称" size="large" />
            </a-form-item>
            <a-form-item label="资产编号" :name="['basicInfo', 'assetNo']" class="basic-three-item">
              <a-input v-model:value="formData.basicInfo.assetNo" placeholder="请输入资产编号" size="large" />
            </a-form-item>
            <a-form-item label="资产类型" :name="['basicInfo', 'assetType']" required class="basic-three-item">
              <a-select v-model:value="formData.basicInfo.assetType" placeholder="请选择资产类型" size="large">
                <a-select-option :value="1">物资/设备</a-select-option>
                <a-select-option :value="2">机动车</a-select-option>
                <a-select-option :value="3">房产</a-select-option>
                <a-select-option :value="4">土地</a-select-option>
                <a-select-option :value="5">其他</a-select-option>
              </a-select>
            </a-form-item>
          </div>

          <!-- 第二行：资产数量、计量单位、是否展示实际数量 -->
          <div class="form-row basic-three-row">
            <a-form-item label="资产数量" :name="['basicInfo', 'quantity']" required class="basic-three-item">
              <a-input v-model:value="formData.basicInfo.quantity" placeholder="请输入资产数量" size="large" />
            </a-form-item>
            <a-form-item label="计量单位" :name="['basicInfo', 'unit']" required class="basic-three-item">
              <a-input v-model:value="formData.basicInfo.unit" placeholder="请输入计量单位" size="large" />
            </a-form-item>
            <a-form-item label="是否展示实际数量" :name="['basicInfo', 'quantityFlag']" class="basic-three-item">
              <a-radio-group v-model:value="formData.basicInfo.quantityFlag">
                <a-radio :value="0">否</a-radio>
                <a-radio :value="1">是</a-radio>
              </a-radio-group>
            </a-form-item>
          </div>

          <!-- 第三行：使用年限、新旧程度、当前状态 -->
          <div class="form-row basic-three-row">
            <a-form-item label="使用年限" :name="['basicInfo', 'serviceLife']" class="basic-three-item">
              <a-input-number
                v-model:value="formData.basicInfo.serviceLife"
                placeholder="请输入使用年限"
                size="large"
                :min="0"
                style="width: 100%"
                :precision="0"
              />
            </a-form-item>
            <a-form-item label="新旧程度" :name="['basicInfo', 'depreciationDegree']" class="basic-three-item">
              <a-select v-model:value="formData.basicInfo.depreciationDegree" placeholder="请选择新旧程度" size="large">
                <a-select-option :value="1">九成新</a-select-option>
                <a-select-option :value="2">八成新</a-select-option>
                <a-select-option :value="3">七成新</a-select-option>
                <a-select-option :value="4">六成新</a-select-option>
                <a-select-option :value="5">五成新</a-select-option>
                <a-select-option :value="6">四成新</a-select-option>
                <a-select-option :value="7">三成新</a-select-option>
                <a-select-option :value="8">二成新</a-select-option>
                <a-select-option :value="9">一成新</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="当前状态" :name="['basicInfo', 'currentStatus']" class="basic-three-item">
              <a-select v-model:value="formData.basicInfo.currentStatus" placeholder="请选择当前状态" size="large">
                <a-select-option :value="1">在用</a-select-option>
                <a-select-option :value="2">闲置</a-select-option>
                <a-select-option :value="3">报废</a-select-option>
              </a-select>
            </a-form-item>
          </div>

          <!-- 第四行：评估价值、处置底价 -->
          <div class="form-row basic-two-row">
            <a-form-item label="评估价值" :name="['basicInfo', 'appraisalValue']" class="basic-two-item">
              <a-input-number
                v-model:value="formData.basicInfo.appraisalValue"
                placeholder="请输入评估价值"
                size="large"
                :min="0"
                style="width: 100%"
                :precision="2"
              />
            </a-form-item>
            <a-form-item label="处置底价" :name="['basicInfo', 'disposalPrice']" class="basic-two-item">
              <a-input-number
                v-model:value="formData.basicInfo.disposalPrice"
                placeholder="请输入处置底价"
                size="large"
                :min="0"
                style="width: 100%"
                :precision="2"
              />
            </a-form-item>
          </div>

          <!-- 第五行：处置时间 -->
          <div class="form-row basic-two-row">
            <a-form-item label="处置开始时间" :name="['basicInfo', 'disposalStartTime']" class="basic-two-item">
              <a-date-picker
                v-model:value="formData.basicInfo.disposalStartTime"
                show-time
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
                placeholder="请选择处置开始时间"
                size="large"
                style="width: 100%"
              />
            </a-form-item>
            <a-form-item label="处置结束时间" :name="['basicInfo', 'disposalEndTime']" class="basic-two-item">
              <a-date-picker
                v-model:value="formData.basicInfo.disposalEndTime"
                show-time
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
                placeholder="请选择处置结束时间"
                size="large"
                style="width: 100%"
              />
            </a-form-item>
          </div>

          <!-- 第六行：付款方式、是否含税 -->
          <div class="form-row basic-two-row">
            <a-form-item label="付款方式" :name="['basicInfo', 'paymentMethod']" class="basic-two-item">
              <a-radio-group v-model:value="formData.basicInfo.paymentMethod">
                <a-radio :value="1">全款</a-radio>
                <a-radio :value="2">分期</a-radio>
              </a-radio-group>
            </a-form-item>
            <a-form-item label="是否含税" :name="['basicInfo', 'isTaxIncluded']" class="basic-two-item">
              <a-select v-model:value="formData.basicInfo.isTaxIncluded" placeholder="请选择是否含税" size="large">
                <a-select-option value="0">不含税</a-select-option>
                <a-select-option value="3">含税3%</a-select-option>
                <a-select-option value="6">含税6%</a-select-option>
                <a-select-option value="9">含税9%</a-select-option>
                <a-select-option value="13">含税13%</a-select-option>
              </a-select>
            </a-form-item>
          </div>
        </div>
      </div>

      <!-- 存放位置板块 -->
      <div class="form-section">
        <div class="section-title">存放位置</div>
        <div class="section-content">
          <div class="form-row location-row">
            <a-form-item label="省市区" :name="['location', 'province']" required class="location-cascader-item">
              <JAreaSelect
                v-model:province="formData.location.province"
                v-model:city="formData.location.city"
                v-model:area="formData.location.area"
                @change="handleAreaChange"
                :level="3"
              />
            </a-form-item>
            <a-form-item label="详细地址" :name="['location', 'detailAddress']" required class="location-address-item">
              <div class="address-input-wrapper">
                <a-input v-model:value="formData.location.detailAddress" placeholder="请输入详细地址" size="large" class="address-input" />
                <a-button type="primary" size="large" class="location-btn" :loading="props.locationLoading" @click="handleGetCurrentLocation">
                  获取当前位置
                </a-button>
              </div>
            </a-form-item>
          </div>
        </div>
      </div>

      <!-- 资料上传板块 -->
      <div class="form-section">
        <div class="section-title">资料上传</div>
        <div class="section-content">
          <div class="form-row">
            <a-form-item label="资产图片" :name="['other', 'images']" class="upload-item">
              <JUpload
                v-model:value="formData.other.images"
                :max-count="10"
                accept="image/*"
                list-type="picture-card"
                upload-text="上传图片"
                :file-size="10"
                :existing-files="formData.hgyAttachmentList"
                biz-type="images"
              />
            </a-form-item>
          </div>

          <div class="form-row">
            <a-form-item label="附件上传" :name="['other', 'attachments']" class="upload-item">
              <JUpload
                v-model:value="formData.other.attachments"
                :max-count="10"
                upload-text="上传附件"
                :file-size="50"
                :existing-files="formData.hgyAttachmentList"
                biz-type="attachments"
              />
            </a-form-item>
          </div>

          <div class="form-row">
            <a-form-item label="委托单上传" :name="['other', 'entrustDocument']" class="upload-item">
              <JUpload
                v-model:value="formData.other.entrustDocument"
                :max-count="5"
                upload-text="上传委托单"
                :file-size="50"
                :existing-files="formData.hgyAttachmentList"
                biz-type="entrustDocument"
              />
            </a-form-item>
          </div>

          <div class="form-row">
            <a-form-item label="特殊说明" :name="['other', 'specialNote']" class="full-width-item">
              <a-textarea v-model:value="formData.other.specialNote" placeholder="请输入特殊说明" :rows="4" :maxlength="500" show-count />
            </a-form-item>
          </div>
        </div>
      </div>
    </a-form>
  </div>
</template>

<script setup lang="ts">
  import { reactive, watch, computed, ref, inject } from 'vue';
  import JAreaSelect from '@/components/Form/src/jeecg/components/JAreaSelect.vue';
  import JUpload from '@/components/Form/src/jeecg/components/JUpload/JUpload.vue';
  import type { PassedReviewItem } from '@/api/manageCenter/appreciationPublish';

  // Props 定义
  interface Props {
    modelValue: any;
    locationLoading?: boolean;
    isEditMode?: boolean;
  }

  // Emits 定义
  interface Emits {
    (e: 'update:modelValue', value: any): void;
    (e: 'area-change', value: any): void;
    (e: 'get-current-location'): void;
    (e: 'service-type-change', value: number): void;
  }

  const props = withDefaults(defineProps<Props>(), {
    locationLoading: false,
    isEditMode: false,
  });

  const emit = defineEmits<Emits>();

  // 注入委托单列表数据
  const entrustOrderList = inject<PassedReviewItem[]>('entrustOrderList', []);
  const entrustOrderLoading = inject<boolean>('entrustOrderLoading', false);

  // 表单数据
  const formData = reactive({ ...props.modelValue });

  // 监听表单数据变化
  watch(
    formData,
    (newVal) => {
      emit('update:modelValue', { ...newVal });
    },
    { deep: true }
  );

  // 监听 props 变化
  watch(
    () => props.modelValue,
    (newVal) => {
      Object.assign(formData, newVal);
    },
    { deep: true }
  );

  // 服务类型变化处理
  const handleServiceTypeChange = (value: number) => {
    emit('service-type-change', value);
  };

  // 委托单选择过滤
  const filterOption = (input: string, option: any) => {
    return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
  };

  // 省市区变化处理
  const handleAreaChange = (value: any) => {
    emit('area-change', value);
  };

  // 获取当前位置
  const handleGetCurrentLocation = () => {
    emit('get-current-location');
  };

  // 表单验证规则
  const rules = computed(() => {
    const baseRules = {
      serviceType: [{ required: true, message: '请选择服务类型', trigger: 'change' }],
      entrustOrderId: [{ required: true, message: '请选择关联委托单号', trigger: 'change' }],
      entrustInfo: {
        title: [{ required: true, message: '委托单位不能为空', trigger: 'blur' }],
        type: [{ required: true, message: '受委托单位不能为空', trigger: 'blur' }],
        noticeName: formData.serviceType === 3 ? [{ required: true, message: '请输入公告名称', trigger: 'blur' }] : [],
      },
      location: {
        province: [{ required: true, message: '请选择省份', trigger: 'change' }],
        city: [{ required: true, message: '请选择城市', trigger: 'change' }],
        area: [{ required: true, message: '请选择区域', trigger: 'change' }],
        detailAddress: [{ required: true, message: '请输入详细地址', trigger: 'blur' }],
      },
    };

    // 资产处置特有验证规则
    if (formData.serviceType === 2) {
      baseRules['basicInfo'] = {
        assetName: [{ required: true, message: '请输入资产名称', trigger: 'blur' }],
        assetType: [{ required: true, message: '请选择资产类型', trigger: 'change' }],
        quantity: [{ required: true, message: '请输入资产数量', trigger: 'blur' }],
        unit: [{ required: true, message: '请输入计量单位', trigger: 'blur' }],
      };
    }

    return baseRules;
  });

  // 表单引用
  const formRef = ref();

  // 验证表单
  const validateForm = async (): Promise<boolean> => {
    try {
      await formRef.value?.validate();
      return true;
    } catch (error) {
      console.error('表单验证失败:', error);
      // 滚动到第一个错误字段
      const firstErrorField = document.querySelector('.ant-form-item-has-error');
      if (firstErrorField) {
        firstErrorField.scrollIntoView({ behavior: 'smooth', block: 'center' });
      }
      return false;
    }
  };

  // 暴露验证方法
  defineExpose({
    validateForm,
  });
</script>

<style lang="less" scoped>
  .step1 {
    .form-section {
      margin-bottom: 32px;
      background: #fafafa;
      border-radius: 8px;
      padding: 24px;

      .section-title {
        font-size: 18px;
        font-weight: 600;
        color: #262626;
        margin-bottom: 24px;
        padding-bottom: 12px;
        border-bottom: 2px solid #004c66;
        position: relative;

        &::after {
          content: '';
          position: absolute;
          bottom: -2px;
          left: 0;
          width: 60px;
          height: 2px;
          background: #004c66;
        }
      }

      .section-content {
        .form-row {
          display: flex;
          gap: 24px;
          margin-bottom: 24px;

          &:last-child {
            margin-bottom: 0;
          }

          // 服务类型行
          &.service-type-row {
            .service-type-item {
              flex: 1;
              margin-bottom: 0;

              .service-type-select {
                width: 100%;
              }

              .edit-mode-tip {
                margin-top: 8px;
                .tip-text {
                  color: #999;
                  font-size: 12px;
                }
              }
            }
          }

          // 委托单号行
          &.entrust-order-row {
            .entrust-order-item {
              flex: 1;
              margin-bottom: 0;

              .entrust-order-select {
                width: 100%;
              }
            }
          }

          // 委托信息两列行
          &.entrust-two-row {
            .entrust-two-item {
              flex: 1;
              margin-bottom: 0;
            }
          }

          // 基本信息三列行
          &.basic-three-row {
            .basic-three-item {
              flex: 1;
              margin-bottom: 0;
            }
          }

          // 基本信息两列行
          &.basic-two-row {
            .basic-two-item {
              flex: 1;
              margin-bottom: 0;
            }
          }

          // 位置行
          &.location-row {
            .location-cascader-item {
              flex: 1;
              margin-bottom: 0;
            }

            .location-address-item {
              flex: 2;
              margin-bottom: 0;

              .address-input-wrapper {
                display: flex;
                gap: 12px;

                .address-input {
                  flex: 1;
                }

                .location-btn {
                  flex-shrink: 0;
                  background-color: #004c66;
                  border-color: #004c66;

                  &:hover:not(:disabled) {
                    background: rgba(0, 76, 102, 0.9);
                  }
                }
              }
            }
          }

          // 全宽度项
          .full-width-item {
            flex: 1;
            margin-bottom: 0;
          }

          // 上传项
          .upload-item {
            flex: 1;
            margin-bottom: 0;
          }
        }
      }
    }

    // 表单项标签样式
    :deep(.ant-form-item-label > label) {
      font-weight: 500;
      color: #262626;
    }

    // 必填项标记样式
    :deep(.ant-form-item-required::before) {
      color: #ff4d4f;
    }

    // 输入框样式
    :deep(.ant-input),
    :deep(.ant-select-selector),
    :deep(.ant-picker) {
      border-radius: 6px;
    }

    // 单选按钮组样式
    :deep(.ant-radio-group) {
      .ant-radio-wrapper {
        margin-right: 24px;
      }
    }
  }

  // 响应式设计
  @media (max-width: 1200px) {
    .step1 {
      .form-section {
        .section-content {
          .form-row {
            &.basic-three-row,
            &.entrust-two-row,
            &.basic-two-row {
              flex-direction: column;
              gap: 16px;
            }

            &.location-row {
              flex-direction: column;
              gap: 16px;

              .location-address-item {
                .address-input-wrapper {
                  flex-direction: column;
                  gap: 12px;
                }
              }
            }
          }
        }
      }
    }
  }

  @media (max-width: 768px) {
    .step1 {
      .form-section {
        padding: 16px;
        margin-bottom: 24px;

        .section-title {
          font-size: 16px;
          margin-bottom: 16px;
        }

        .section-content {
          .form-row {
            flex-direction: column;
            gap: 12px;
            margin-bottom: 16px;
          }
        }
      }
    }
  }
</style>
